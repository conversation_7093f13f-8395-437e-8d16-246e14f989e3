"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { DocumentService } from "@/lib/api/services/document";

export async function GET(request: NextRequest) {
  try {
    // Initialize document service
    const session = await auth();
    const documentService = new DocumentService({
      requireAuth: true,
    });

    // Set the service context with session and request
    documentService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Extract query parameters from URL
    const { searchParams } = new URL(request.url);
    const queryParams: any = Object.fromEntries(searchParams.entries());

    // Use the service to get documents
    const result = await documentService.getDocuments(queryParams);

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Documents GET error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
