import { NextRequest, NextResponse } from "next/server";

import { auth } from "@/lib/auth";
import { RoleService } from "@/lib/api/services/role";

/**
 * GET /api/roles
 * Get roles with optional filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Get session for authentication
    const session = await auth();

    // Initialize role service
    const roleService = new RoleService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    roleService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Extract query parameters from URL
    const { searchParams } = new URL(request.url);
    const queryParams: any = Object.fromEntries(searchParams.entries());

    // Use the service to get roles
    const result = await roleService.getRoles(queryParams);

    // Return standardized response format
    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, {
        status: result.statusCode || 500,
      });
    }
  } catch (error) {
    return NextResponse.json(error, { status: 500 });
  }
}

/**
 * POST /api/roles
 * Create a new role
 */
export async function POST(request: NextRequest) {
  try {
    // Get session for authentication
    const session = await auth();

    // Initialize role service
    const roleService = new RoleService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    roleService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Use the service to create role
    const result = await roleService.createRole(body);

    // Return standardized response format
    return NextResponse.json(
      {
        success: true,
        error: false,
        data: result,
        message: "Role created successfully",
        timestamp: new Date().toISOString(),
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Role creation error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/roles
 * Update an existing role
 */
export async function PUT(request: NextRequest) {
  try {
    // Get session for authentication
    const session = await auth();

    // Initialize role service
    const roleService = new RoleService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    roleService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Use the service to update role
    const result = await roleService.updateRole(body);

    // Return standardized response format
    return NextResponse.json(
      {
        success: true,
        error: false,
        data: result,
        message: "Role updated successfully",
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Role update error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/roles
 * Delete a role by ID (passed in request body)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get session for authentication
    const session = await auth();

    // Initialize role service
    const roleService = new RoleService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    roleService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Parse request body to get role ID
    const body = await request.json();
    const { id } = body;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Role ID is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Use the service to delete role
    const result = await roleService.deleteRole(id);

    // Return standardized response format
    return NextResponse.json(
      {
        success: true,
        error: false,
        data: result,
        message: "Role deleted successfully",
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Role deletion error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
