import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RoleService } from "@/lib/api/services/role";

/**
 * GET /api/roles/[id]
 * Get a specific role by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get session for authentication
    const session = await auth();

    // Initialize role service
    const roleService = new RoleService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    roleService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Use the service to get the specific role
    const result = await roleService.getRoles({ id: params.id });

    // Return standardized response format
    return NextResponse.json(
      {
        success: true,
        error: false,
        data: result,
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Role GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/roles/[id]
 * Update a specific role by ID
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get session for authentication
    const session = await auth();

    // Initialize role service
    const roleService = new RoleService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    roleService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Parse request body and add the ID from params
    const body = await request.json();
    const updateData = { ...body, id: params.id };

    // Use the service to update the role
    const result = await roleService.updateRole(updateData);

    // Return standardized response format
    return NextResponse.json(
      {
        success: true,
        error: false,
        data: result,
        message: "Role updated successfully",
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Role update error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/roles/[id]
 * Delete a specific role by ID
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get session for authentication
    const session = await auth();

    // Initialize role service
    const roleService = new RoleService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    roleService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Use the service to delete the role
    const result = await roleService.deleteRole(params.id);

    // Return standardized response format
    return NextResponse.json(
      {
        success: true,
        error: false,
        data: result,
        message: "Role deleted successfully",
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Role deletion error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
