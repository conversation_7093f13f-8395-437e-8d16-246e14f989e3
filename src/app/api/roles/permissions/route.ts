import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RoleService } from "@/lib/api/services/role";

/**
 * PUT /api/roles/permissions
 * Update role permissions
 */
export async function PUT(request: NextRequest) {
  try {
    // Get session for authentication
    const session = await auth();

    // Initialize role service
    const roleService = new RoleService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    roleService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Use the service to update role permissions
    const result = await roleService.updateRolePermissions(body);

    // Return standardized response format
    return NextResponse.json(
      {
        success: true,
        error: false,
        data: result,
        message: "Role permissions updated successfully",
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Role permissions update error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
