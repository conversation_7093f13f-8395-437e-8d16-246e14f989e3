import { BaseService } from "./base";
import {
  ContractSchema,
  CreateContractSchema,
  UpdateContractSchema,
  ContractStatisticsSchema,
  type Contract,
  type CreateContract,
  type UpdateContract,
  type ContractStatistics,
} from "../validators/schemas/contract";
import { mockContracts, mockContractStatistics } from "@/data/contracts-mock";

export class ContractService extends BaseService {
  private contracts: Contract[] = [];

  constructor() {
    super();
    // Initialize with mock data
    this.initializeMockData();
  }

  private initializeMockData() {
    // Convert mock data to API format
    this.contracts = mockContracts.map((contract) => ({
      id: contract.id,
      title: contract.title,
      description: contract.description,
      status: contract.status as Contract["status"],
      client_name: contract.clientName,
      contract_value: contract.contractValue,
      start_date: contract.startDate,
      end_date: contract.endDate,
      proposal_id: contract.proposalId,
      createdAt: contract.createdDate,
      updatedAt: contract.lastModified,
    }));
  }

  /**
   * Get all contracts
   */
  async getContracts(): Promise<any> {
    try {
      this.log("info", "Fetching all contracts");

      // Simulate API delay
      await this.delay(500);

      this.log("info", `Found ${this.contracts.length} contracts`);

      return this.createSuccessResponse(this.contracts, 200, "Contracts retrieved successfully");
    } catch (error) {
      this.log("error", `Error fetching contracts: ${error}`);
      return this.createErrorResponse(
        "Failed to fetch contracts",
        500
      );
    }
  }

  /**
   * Get contract by ID
   */
  async getContract(id: string): Promise<any> {
    try {
      this.log("info", `Fetching contract with ID: ${id}`);

      // Simulate API delay
      await this.delay(300);

      const contract = this.contracts.find((c) => c.id === id);

      if (!contract) {
        this.log("warn", `Contract not found: ${id}`);
        return this.createErrorResponse("Contract not found", 404);
      }

      this.log("info", `Contract found: ${contract.title}`);

      return this.createSuccessResponse(contract, 200, "Contract retrieved successfully");
    } catch (error) {
      this.log("error", `Error fetching contract: ${error}`);
      return this.createErrorResponse(
        "Failed to fetch contract",
        500
      );
    }
  }

  /**
   * Create a new contract
   */
  async createContract(contractData: CreateContract): Promise<any> {
    try {
      this.log("info", "Creating new contract", { title: contractData.title });

      // Validate input
      const validatedData = CreateContractSchema.parse(contractData);

      // Simulate API delay
      await this.delay(800);

      // Create new contract
      const newContract: Contract = {
        id: `contract-${Date.now()}`,
        ...validatedData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Validate the created contract
      const validatedContract = ContractSchema.parse(newContract);

      // Add to contracts array
      this.contracts.push(validatedContract);

      this.log("info", `Contract created successfully: ${validatedContract.id}`);

      return this.createSuccessResponse(validatedContract, 201, "Contract created successfully");
    } catch (error) {
      this.log("error", `Contract creation error: ${error}`);
      return this.createErrorResponse(
        "An unexpected error occurred during contract creation",
        500
      );
    }
  }

  /**
   * Update an existing contract
   */
  async updateContract(id: string, contractData: Partial<UpdateContract>): Promise<any> {
    try {
      this.log("info", `Updating contract: ${id}`, contractData);

      // Find existing contract
      const existingContractIndex = this.contracts.findIndex((c) => c.id === id);

      if (existingContractIndex === -1) {
        this.log("warn", `Contract not found for update: ${id}`);
        return this.createErrorResponse("Contract not found", 404);
      }

      // Simulate API delay
      await this.delay(600);

      // Update contract
      const updatedContract = {
        ...this.contracts[existingContractIndex],
        ...contractData,
        updatedAt: new Date().toISOString(),
      };

      // Validate updated contract
      const validatedContract = ContractSchema.parse(updatedContract);

      // Update in array
      this.contracts[existingContractIndex] = validatedContract;

      this.log("info", `Contract updated successfully: ${id}`);

      return this.createSuccessResponse(validatedContract, 200, "Contract updated successfully");
    } catch (error) {
      this.log("error", `Contract update error: ${error}`);
      return this.createErrorResponse(
        "An unexpected error occurred during contract update",
        500
      );
    }
  }

  /**
   * Delete a contract
   */
  async deleteContract(id: string): Promise<any> {
    try {
      this.log("info", `Deleting contract: ${id}`);

      // Find contract
      const contractIndex = this.contracts.findIndex((c) => c.id === id);

      if (contractIndex === -1) {
        this.log("warn", `Contract not found for deletion: ${id}`);
        return this.createErrorResponse("Contract not found", 404);
      }

      // Simulate API delay
      await this.delay(400);

      // Remove contract
      this.contracts.splice(contractIndex, 1);

      this.log("info", `Contract deleted successfully: ${id}`);

      return this.createSuccessResponse(null, 200, "Contract deleted successfully");
    } catch (error) {
      this.log("error", `Contract deletion error: ${error}`);
      return this.createErrorResponse(
        "An unexpected error occurred during contract deletion",
        500
      );
    }
  }

  /**
   * Get contract statistics
   */
  async getContractStatistics(): Promise<any> {
    try {
      this.log("info", "Fetching contract statistics");

      // Simulate API delay
      await this.delay(300);

      // Calculate statistics from current contracts
      const statistics: ContractStatistics = {
        total: this.contracts.length,
        active: this.contracts.filter(c => c.status === "active").length,
        completed: this.contracts.filter(c => c.status === "completed").length,
        draft: this.contracts.filter(c => c.status === "draft").length,
        terminated: this.contracts.filter(c => c.status === "terminated").length,
        expired: this.contracts.filter(c => c.status === "expired").length,
        totalValue: this.contracts.reduce((sum, c) => sum + c.contract_value, 0),
        averageValue: this.contracts.length > 0 
          ? this.contracts.reduce((sum, c) => sum + c.contract_value, 0) / this.contracts.length 
          : 0,
      };

      // Validate statistics
      const validatedStatistics = ContractStatisticsSchema.parse(statistics);

      this.log("info", "Contract statistics calculated", validatedStatistics);

      return this.createSuccessResponse(validatedStatistics, 200, "Statistics retrieved successfully");
    } catch (error) {
      this.log("error", `Error fetching contract statistics: ${error}`);
      return this.createErrorResponse(
        "Failed to fetch contract statistics",
        500
      );
    }
  }
}
