# RBAC (Role-Based Access Control) Library

A comprehensive RBAC system for React applications with TypeScript support, built for dynamic role and permission management.

## Features

- 🔐 **Role-Based Access Control**: Flexible role and permission system
- ⚡ **React Integration**: Context, hooks, and component wrappers
- 🎯 **Dynamic Permissions**: Runtime permission checking with caching
- 🔧 **Management UI**: Built-in dialogs for user-role and role-permission management
- 📱 **Responsive Design**: Mobile-friendly management interfaces
- 🎨 **Customizable**: Flexible fallback components and styling
- 🚀 **Performance**: Permission caching and optimized checks

## Quick Start

### 1. Setup the Provider

Wrap your app with the RBAC provider:

```tsx
import { RBACProvider } from '@/lib/rbac';

function App() {
  return (
    <RBACProvider config={{ enableLogging: true, cachePermissions: true }}>
      {/* Your app content */}
    </RBACProvider>
  );
}
```

### 2. Use Component Wrappers

Protect components with permission checks:

```tsx
import { CanCreate, CanUpdate, RBACWrapper } from '@/components/common/rbac';

function DocumentActions() {
  return (
    <div>
      <CanCreate entity="document">
        <button>Create Document</button>
      </CanCreate>
      
      <CanUpdate entity="document">
        <button>Edit Document</button>
      </CanUpdate>
      
      <RBACWrapper 
        entity="document" 
        action="delete"
        fallback={<span>No delete permission</span>}
      >
        <button>Delete Document</button>
      </RBACWrapper>
    </div>
  );
}
```

### 3. Use Hooks for Logic

Check permissions in your components:

```tsx
import { useRBACPermissions, useEntityPermissions } from '@/lib/rbac';

function MyComponent() {
  const { hasPermission } = useRBACPermissions();
  const documentPerms = useEntityPermissions('document');
  
  const canCreateDocument = hasPermission('document', 'create');
  
  return (
    <div>
      {documentPerms.canRead && <DocumentList />}
      {documentPerms.canCreate && <CreateButton />}
    </div>
  );
}
```

## Core Concepts

### Entities
Entities represent the resources in your system:
- `user`, `role`, `permission`
- `project`, `document`, `contract`
- Custom entities as needed

### Actions
Standard CRUD operations:
- `create` - Create new resources
- `read` - View/access resources  
- `update` - Modify existing resources
- `delete` - Remove resources

### Roles
Collections of permissions:
- Can be assigned to users
- Support hierarchical permissions
- System roles vs custom roles

## Component API

### RBACWrapper
Main wrapper component for conditional rendering:

```tsx
<RBACWrapper
  entity="document"
  action="create"
  resourceId="doc-123"          // Optional resource-specific check
  fallback={<div>No access</div>} // Shown when permission denied
  requireAll={true}             // For multiple permissions
  showLoader={true}             // Show loading state
>
  <ProtectedContent />
</RBACWrapper>
```

### Specialized Wrappers
Convenience wrappers for common operations:

```tsx
<CanCreate entity="project">
  <CreateProjectButton />
</CanCreate>

<CanRead entity="document" resourceId="doc-123">
  <DocumentViewer />
</CanRead>

<AdminOnly>
  <AdminPanel />
</AdminOnly>
```

### Conditional Rendering
Show different content based on permissions:

```tsx
<RBACConditional
  entity="project"
  action="create"
  granted={<ManagerView />}
  denied={<ReadOnlyView />}
  loading={<LoadingSpinner />}
/>
```

## Hooks API

### useRBACPermissions
Main hook for permission checking:

```tsx
const {
  hasPermission,      // Quick boolean check
  checkPermission,    // Detailed async check
  userRoles,         // Current user roles
  userPermissions,   // Current user permissions
  isLoading,         // Loading state
  error              // Error state
} = useRBACPermissions();
```

### useEntityPermissions
Get all CRUD permissions for an entity:

```tsx
const {
  canCreate,
  canRead,
  canUpdate,
  canDelete
} = useEntityPermissions('document');
```

### useMultiplePermissions
Check multiple permissions at once:

```tsx
const {
  results,      // Array of permission results
  allGranted,   // True if all permissions granted
  anyGranted    // True if any permission granted
} = useMultiplePermissions([
  { entity: 'user', action: 'create' },
  { entity: 'role', action: 'update' }
]);
```

### useRBACManagement
Administrative operations:

```tsx
const {
  users,
  roles,
  permissions,
  assignRoleToUser,
  revokeRoleFromUser,
  createRole,
  updateRole,
  deleteRole,
  isLoading,
  refresh
} = useRBACManagement();
```

## Management Dialogs

### User Role Assignment
Dialog for managing user-to-role assignments:

```tsx
import { UserRoleDialog } from '@/components/common/rbac';

<UserRoleDialog
  open={dialogOpen}
  onOpenChange={setDialogOpen}
  selectedUserId="user-123"     // Optional pre-selected user
  onSuccess={() => {
    // Handle success
  }}
/>
```

### Role Permission Management
Dialog for managing role permissions:

```tsx
import { RolePermissionDialog } from '@/components/common/rbac';

<RolePermissionDialog
  open={dialogOpen}
  onOpenChange={setDialogOpen}
  mode="create"                 // 'create' | 'edit' | 'view'
  roleId="role-123"            // For edit/view modes
  onSuccess={() => {
    // Handle success
  }}
/>
```

## Configuration

### RBAC Provider Config
```tsx
interface RBACConfig {
  enableLogging?: boolean;      // Enable permission check logging
  cachePermissions?: boolean;   // Cache permission results
  cacheTTL?: number;           // Cache time-to-live (ms)
  defaultRoles?: string[];     // Default roles for new users
}
```

### Default Entities
Pre-defined entities for common use cases:

```tsx
import { DEFAULT_ENTITIES } from '@/lib/rbac';

// Available entities:
DEFAULT_ENTITIES.USER
DEFAULT_ENTITIES.ROLE
DEFAULT_ENTITIES.PERMISSION
DEFAULT_ENTITIES.PROJECT
DEFAULT_ENTITIES.DOCUMENT
DEFAULT_ENTITIES.CONTRACT
DEFAULT_ENTITIES.PROPOSAL
DEFAULT_ENTITIES.CLIENT
DEFAULT_ENTITIES.PRODUCT
DEFAULT_ENTITIES.CATEGORY
DEFAULT_ENTITIES.MEDIA
```

## Integration with Existing Auth

The RBAC system integrates with NextAuth.js and your existing authentication:

```tsx
// The system automatically uses the current session
// and fetches user roles/permissions based on the authenticated user
```

## Demo Component

Use the demo component to test and showcase RBAC functionality:

```tsx
import { RBACDemo } from '@/components/common/rbac';

<RBACDemo />
```

## Best Practices

1. **Use Specific Permissions**: Prefer specific entity-action combinations over broad permissions
2. **Cache Wisely**: Enable caching for better performance, but consider cache invalidation
3. **Fallback Components**: Always provide meaningful fallback content
4. **Error Handling**: Handle loading and error states appropriately
5. **Resource-Specific**: Use resourceId for fine-grained permissions when needed
6. **Role Hierarchy**: Design roles with clear hierarchies and minimal overlap

## TypeScript Support

Full TypeScript support with comprehensive type definitions:

```tsx
import type { 
  Permission, 
  Role, 
  RBACUser, 
  EntityType, 
  PermissionAction 
} from '@/lib/rbac/types';
```

## Performance Considerations

- Permission checks are cached by default
- Use `clearCache()` or `clearUserCache()` when permissions change
- Batch permission updates for better performance
- Consider using `useMultiplePermissions` for multiple checks
