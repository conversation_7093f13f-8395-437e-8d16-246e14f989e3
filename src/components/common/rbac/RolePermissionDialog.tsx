"use client";

/**
 * Role Permission Management Dialog
 *
 * A dialog component for managing role permissions with CRUD operations
 * (create, update, view, delete) for different entities.
 */

import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/common/ui/dialog";
import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import { Badge } from "@/components/common/ui/badge";
import { Checkbox } from "@/components/common/ui/checkbox";
import { Textarea } from "@/components/common/ui/textarea";

import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/common/ui/tabs";
import { useRoleManagement } from "@/hooks/useRBAC";
import { useAuth } from "@/hooks/useAuth";
// Types are used in type annotations and interfaces
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from "@/lib/rbac";

import { Shield, Save, Trash2, FileText } from "lucide-react";

interface RolePermissionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  roleId: string; // Required for edit context
  mode?: "edit" | "view"; // Only edit and view modes
  onSuccess?: () => void;
}

interface PermissionMatrix {
  [entity: string]: {
    [action: string]: boolean;
  };
}

export function RolePermissionDialog({
  open,
  onOpenChange,
  roleId,
  mode = "edit",
  onSuccess,
}: RolePermissionDialogProps) {
  const { roles, updateRole, deleteRole, error } = useRoleManagement();

  // Get current user's role and permissions from useAuth
  const { role: currentUserRole } = useAuth();

  // Check if current user can manage roles based on their permissions
  // Support both legacy string array and new JSON object structure
  const canManageRoles = useMemo(() => {
    if (!currentUserRole?.permissions) return false;

    // Handle new JSON-based permissions structure
    if (
      typeof currentUserRole.permissions === "object" &&
      !Array.isArray(currentUserRole.permissions)
    ) {
      const permissions = currentUserRole.permissions as Record<
        string,
        string[]
      >;
      const rolePermissions = permissions.role || [];
      return (
        rolePermissions.includes("create") ||
        rolePermissions.includes("update") ||
        rolePermissions.includes("delete") ||
        (permissions.admin && permissions.admin.includes("all"))
      );
    }

    // Handle legacy string array permissions
    if (Array.isArray(currentUserRole.permissions)) {
      return (
        currentUserRole.permissions.includes("role:manage") ||
        currentUserRole.permissions.includes("admin:all")
      );
    }

    return false;
  }, [currentUserRole?.permissions]);

  const [roleName, setRoleName] = useState("");
  const [roleDescription, setRoleDescription] = useState("");
  const [permissionMatrix, setPermissionMatrix] = useState<PermissionMatrix>(
    {}
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string>();
  const [activeTab, setActiveTab] = useState("details");

  // Get current role data
  const currentRole = Array.isArray(roles)
    ? roles.find((role) => role.id === roleId)
    : undefined;

  // Available entities based on current user's permissions
  // Show entities that the current user can manage, plus entities from the role being edited
  const availableEntities = useMemo(() => {
    const entitiesSet = new Set<string>();

    // Add entities from the role being edited (so user can see what permissions the role has)
    const permissions = currentUserRole?.permissions as Record<
      string,
      string[]
    >;

    if (currentRole?.permissions) {
      // JSON-based permissions
      Object.keys(currentRole.permissions).forEach((entity) => {
        entitiesSet.add(entity);
      });

      // Add entities the user has permissions for
      Object.keys(permissions).forEach((entity) => {
        if (permissions[entity] && permissions[entity].length > 0) {
          entitiesSet.add(entity);
        }
      });
    }

    // If no entities found, add default entities based on permissions.json
    if (entitiesSet.size === 0) {
      [
        "user",
        "role",
        "document",
        "proposal",
        "contract",
        "room",
        "member",
      ].forEach((entity) => {
        entitiesSet.add(entity);
      });
    }

    const result = Array.from(entitiesSet).sort();
    return result;
  }, [currentUserRole?.permissions, currentRole?.permissions]);

  // Initialize form when dialog opens or role changes
  useEffect(() => {
    if (open && currentRole) {
      setRoleName(currentRole.name);
      setRoleDescription(currentRole.description || "");

      // Build permission matrix from current role permissions (JSON-based structure)
      const matrix: PermissionMatrix = {};
      availableEntities.forEach((entity) => {
        matrix[entity] = {};
        Object.values(PERMISSION_ACTIONS).forEach((action) => {
          let hasPermission = false;

          if (currentRole.permissions) {
            // Handle JSON-based permissions structure
            if (
              typeof currentRole.permissions === "object" &&
              !Array.isArray(currentRole.permissions)
            ) {
              const entityPermissions = currentRole.permissions[entity];
              hasPermission =
                Array.isArray(entityPermissions) &&
                entityPermissions.includes(action);
            }
            // Handle legacy string array permissions (backward compatibility)
            else if (Array.isArray(currentRole.permissions)) {
              const permissionString = `${entity}:${action}`;
              hasPermission =
                currentRole.permissions.includes(permissionString);
            }
          }

          matrix[entity][action] = hasPermission;
        });
      });

      setPermissionMatrix(matrix);
    }
    setSubmitError(undefined);
  }, [
    open,
    currentRole?.id,
    currentRole?.name,
    currentRole?.description,
    availableEntities,
  ]);

  // Check if current user can grant specific permissions
  const canGrantPermission = useCallback(
    (entity: string, action: string) => {
      if (!currentUserRole?.permissions) return false;

      // Handle new JSON-based permissions structure
      if (
        typeof currentUserRole.permissions === "object" &&
        !Array.isArray(currentUserRole.permissions)
      ) {
        const permissions = currentUserRole.permissions as Record<
          string,
          string[]
        >;

        // Admin can grant any permission
        if (permissions.admin && permissions.admin.includes("all")) {
          return true;
        }

        // User can only grant permissions they have themselves
        const entityPermissions = permissions[entity] || [];
        return entityPermissions.includes(action);
      }

      // Handle legacy string array permissions
      if (Array.isArray(currentUserRole.permissions)) {
        // Admin can grant any permission
        if (currentUserRole.permissions.includes("admin:all")) {
          return true;
        }

        // User can only grant permissions they have themselves
        const permissionString = `${entity}:${action}`;
        return currentUserRole.permissions.includes(permissionString);
      }

      return false;
    },
    [currentUserRole?.permissions]
  );

  const handlePermissionToggle = (entity: string, action: string) => {
    if (mode === "view") return;

    // Check if user can grant this permission
    if (!canGrantPermission(entity, action)) {
      return; // Silently ignore if user doesn't have permission
    }

    setPermissionMatrix((prev) => ({
      ...prev,
      [entity]: {
        ...prev[entity],
        [action]: !prev[entity]?.[action],
      },
    }));
  };

  const handleSelectAllForEntity = (entity: string, selected: boolean) => {
    if (mode === "view") return;

    setPermissionMatrix((prev) => ({
      ...prev,
      [entity]: Object.values(PERMISSION_ACTIONS).reduce(
        (acc, action) => ({
          ...acc,
          // Only set to selected if user can grant this permission
          [action]:
            selected && canGrantPermission(entity, action)
              ? selected
              : prev[entity]?.[action] || false,
        }),
        {}
      ),
    }));
  };

  const handleSubmit = async () => {
    if (!roleName.trim()) {
      setSubmitError("Role name is required");
      return;
    }

    // Check if user has permission to manage roles
    if (!canManageRoles) {
      setSubmitError("You don't have permission to manage roles");
      return;
    }

    // Validate that user can grant all selected permissions
    const invalidPermissions: string[] = [];
    Object.entries(permissionMatrix).forEach(([entity, actions]) => {
      Object.entries(actions).forEach(([action, enabled]) => {
        if (enabled && !canGrantPermission(entity, action)) {
          invalidPermissions.push(`${entity}:${action}`);
        }
      });
    });

    if (invalidPermissions.length > 0) {
      setSubmitError(
        `You don't have permission to grant: ${invalidPermissions.join(", ")}`
      );
      return;
    }

    setIsSubmitting(true);
    setSubmitError(undefined);

    try {
      let targetRoleId = roleId;

      // Update role (edit mode only)
      if (mode === "edit" && currentRole) {
        const result = await updateRole(currentRole.id, {
          name: roleName.trim(),
          description: roleDescription.trim(),
        });
        if (!result.success) {
          setSubmitError(result.error || "Failed to update role");
          return;
        }
      }

      if (!targetRoleId) {
        setSubmitError("Failed to get role ID");
        return;
      }

      // Update permissions using the new JSON-based structure
      if (mode !== "view") {
        // Convert permission matrix to JSON-based permissions object
        const newPermissions: Record<string, string[]> = {};
        Object.entries(permissionMatrix).forEach(([entity, actions]) => {
          const enabledActions = Object.entries(actions)
            .filter(([_, enabled]) => enabled)
            .map(([action, _]) => action);
          if (enabledActions.length > 0) {
            newPermissions[entity] = enabledActions;
          }
        });

        // Update the role with new permissions structure
        try {
          const updateResult = await updateRole(targetRoleId, {
            permissions: newPermissions,
          });

          if (!updateResult.success) {
            setSubmitError(
              updateResult.error || "Failed to update permissions"
            );
            return;
          }
        } catch (error) {
          console.error("Error updating permissions:", error);
          setSubmitError("Failed to update permissions");
          return;
        }
      }

      // Success - refresh data and close dialog
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      setSubmitError(
        error instanceof Error ? error.message : "Failed to save role"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!currentRole || mode !== "edit") return;

    if (
      !confirm(
        `Are you sure you want to delete the role "${currentRole.name}"? This action cannot be undone.`
      )
    ) {
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await deleteRole(currentRole.id);
      if (!result.success) {
        setSubmitError(result.error || "Failed to delete role");
        return;
      }

      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      setSubmitError(
        error instanceof Error ? error.message : "Failed to delete role"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const getPermissionCount = () => {
    return Object.values(permissionMatrix).reduce(
      (total, entityPerms) =>
        total + Object.values(entityPerms).filter(Boolean).length,
      0
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex flex-row items-center gap-4">
            <Shield className="h-5 w-5" />
            Edit Role
          </DialogTitle>
          <DialogDescription>
            Modify role details and permissions based on your current
            permissions
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="details" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Role Details
            </TabsTrigger>
            <TabsTrigger
              value="permissions"
              className="flex items-center gap-2"
            >
              <Shield className="h-4 w-4" />
              Permissions ({getPermissionCount()})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="role-name">Role Name *</Label>
                <Input
                  id="role-name"
                  value={roleName}
                  onChange={(e) => setRoleName(e.target.value)}
                  placeholder="Enter role name"
                  disabled={mode === "view"}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="role-description">Description</Label>
                <Textarea
                  id="role-description"
                  value={roleDescription}
                  onChange={(e) => setRoleDescription(e.target.value)}
                  placeholder="Enter role description"
                  rows={3}
                  disabled={mode === "view"}
                />
              </div>

              {currentRole && (
                <div className="space-y-2">
                  <Label>Role Information</Label>
                  <div className="grid grid-cols-2 gap-4 p-3 bg-gray-50 rounded-lg">
                    <div>
                      <span className="text-sm font-medium">Created:</span>
                      <p className="text-sm text-gray-600">
                        {currentRole.createdAt.toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Last Updated:</span>
                      <p className="text-sm text-gray-600">
                        {currentRole.updatedAt.toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  {currentRole.isSystem && (
                    <Badge variant="outline" className="w-fit">
                      System Role
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="permissions" className="space-y-4">
            <div className="space-y-4">
              {availableEntities.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No entities available to manage permissions for.</p>
                  <p className="text-sm mt-2">
                    This might be because the role has no permissions or you
                    don't have permission to view them.
                  </p>
                </div>
              ) : (
                availableEntities.map((entity) => {
                  const entityPermissions = permissionMatrix[entity] || {};
                  const allSelected = Object.values(PERMISSION_ACTIONS).every(
                    (action) => entityPermissions[action]
                  );
                  const someSelected = Object.values(PERMISSION_ACTIONS).some(
                    (action) => entityPermissions[action]
                  );

                  return (
                    <div key={entity} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={allSelected}
                            ref={(el) => {
                              if (el && el instanceof HTMLInputElement) {
                                el.indeterminate = someSelected && !allSelected;
                              }
                            }}
                            onCheckedChange={(checked) =>
                              handleSelectAllForEntity(
                                entity,
                                checked as boolean
                              )
                            }
                            disabled={
                              mode === "view" ||
                              !Object.values(PERMISSION_ACTIONS).some(
                                (action) => canGrantPermission(entity, action)
                              )
                            }
                            title={
                              !Object.values(PERMISSION_ACTIONS).some(
                                (action) => canGrantPermission(entity, action)
                              )
                                ? "You don't have permission to modify any actions for this entity"
                                : "Select/deselect all permissions for this entity"
                            }
                          />
                          <Label className="font-medium capitalize">
                            {entity.replace("_", " ")}
                            {!Object.values(PERMISSION_ACTIONS).some((action) =>
                              canGrantPermission(entity, action)
                            ) && (
                              <span className="ml-2 text-xs text-orange-500 font-normal">
                                (read-only)
                              </span>
                            )}
                          </Label>
                        </div>
                        <Badge variant="secondary">
                          {
                            Object.values(entityPermissions).filter(Boolean)
                              .length
                          }{" "}
                          / {Object.values(PERMISSION_ACTIONS).length}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 ml-6">
                        {Object.values(PERMISSION_ACTIONS).map((action) => {
                          const isChecked = entityPermissions[action] || false;
                          const canModify = canGrantPermission(entity, action);
                          const isDisabled =
                            mode === "view" || (isChecked && !canModify);

                          return (
                            <div
                              key={action}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`${entity}-${action}`}
                                checked={isChecked}
                                onCheckedChange={() =>
                                  handlePermissionToggle(entity, action)
                                }
                                disabled={isDisabled}
                                className={
                                  isChecked && !canModify
                                    ? "opacity-60 cursor-not-allowed"
                                    : ""
                                }
                              />
                              <Label
                                htmlFor={`${entity}-${action}`}
                                className={`text-sm capitalize ${
                                  isDisabled
                                    ? "text-gray-500 cursor-not-allowed"
                                    : "cursor-pointer"
                                } ${
                                  isChecked && !canModify
                                    ? "text-orange-600 font-medium"
                                    : ""
                                }`}
                                title={
                                  isChecked && !canModify
                                    ? "You don't have permission to modify this"
                                    : canModify
                                    ? "You can modify this permission"
                                    : "You don't have permission to grant this"
                                }
                              >
                                {action}
                                {isChecked && !canModify && (
                                  <span className="ml-1 text-xs text-orange-500">
                                    (read-only)
                                  </span>
                                )}
                              </Label>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </TabsContent>
        </Tabs>

        {/* Error Display */}
        {(error || submitError) && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{error || submitError}</p>
          </div>
        )}

        <DialogFooter className="flex justify-between">
          <div>
            {mode === "edit" && currentRole && !currentRole.isSystem && (
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={isSubmitting}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Role
              </Button>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              {mode === "view" ? "Close" : "Cancel"}
            </Button>

            {mode !== "view" && (
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || !roleName.trim()}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
